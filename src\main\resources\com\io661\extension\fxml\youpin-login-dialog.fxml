<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
      fx:controller="com.io661.extension.controller.YouPinLoginDialogController"
      styleClass="youpin-login-dialog" prefWidth="400" prefHeight="300"
      stylesheets="@../css/youpin-login-dialog.css" spacing="20">
    <padding>
        <Insets top="20" right="20" bottom="20" left="20" />
    </padding>

    <!-- 标题区域 -->
    <HBox alignment="CENTER" spacing="10">
        <ImageView fitHeight="32.0" fitWidth="32.0">
            <Image url="@../img/youpin.png" />
        </ImageView>
        <Label text="绑定悠悠有品账号" styleClass="dialog-title">
            <font>
                <Font name="System Bold" size="16.0" />
            </font>
        </Label>
    </HBox>

    <!-- 手机号输入区域 -->
    <VBox spacing="8">
        <Label text="手机号:" styleClass="input-label" />
        <TextField fx:id="phoneField" promptText="请输入手机号" styleClass="input-field" />
    </VBox>

    <!-- 验证码输入区域 -->
    <VBox spacing="8">
        <HBox alignment="CENTER_LEFT" spacing="10">
            <Label text="验证码:" styleClass="input-label" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="sendCodeButton" text="发送验证码" styleClass="send-code-button"
                    onAction="#handleSendCode" />
        </HBox>
        <TextField fx:id="codeField" promptText="请输入验证码" styleClass="input-field" />
    </VBox>

    <!-- 短信发送提示区域 -->
    <VBox fx:id="smsPromptBox" visible="false" managed="false" spacing="10"
          styleClass="sms-prompt-box">
        <Label fx:id="smsPromptLabel" text="" styleClass="sms-prompt-text" wrapText="true" />
        <HBox spacing="10" alignment="CENTER">
            <Button fx:id="cancelSmsButton" text="取消" styleClass="secondary-button"
                    onAction="#handleCancelSms" />
            <Button fx:id="confirmSmsButton" text="我已发送" styleClass="primary-button"
                    onAction="#handleConfirmSms" />
        </HBox>
    </VBox>

    <Region VBox.vgrow="ALWAYS" />

    <!-- 按钮区域 -->
    <HBox spacing="10" alignment="CENTER">
        <Button fx:id="cancelButton" text="取消" styleClass="secondary-button"
                onAction="#handleCancel" />
        <Button fx:id="loginButton" text="登录" styleClass="primary-button"
                onAction="#handleLogin" />
    </HBox>
</VBox>
