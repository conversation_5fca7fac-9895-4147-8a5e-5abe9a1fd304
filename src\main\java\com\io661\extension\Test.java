package com.io661.extension;

import com.io661.extension.commonURL.CommonYouPinHttpUrl;
import com.io661.extension.service.Impl.YouPinServiceImpl;
import com.io661.extension.util.YouPin.YouPinCookieManager;

import java.io.IOException;
import java.util.Arrays;

import static com.io661.extension.util.SQLite.SqliteManager.queryYouPinCookie;

public class Test {
    public static void main(String[] args) throws IOException {
        CommonYouPinHttpUrl youPinHttpClient = new CommonYouPinHttpUrl();
        String steamId = "*****************";

        YouPinServiceImpl youPinService = new YouPinServiceImpl();


        // buffcookies
//        String session = startGetBuffCookies();
//
//        System.out.println(session);


//        System.out.println(youPinService.sendSmsCode("***********"));


//        System.out.println(youPinService.loginAccount("***********", ""));


        String token = YouPinCookieManager.readYouPinCookie(steamId);

        System.out.println("获取token:" + token);

//        System.out.println(queryYouPinCookie("*****************"));
        System.out.println(youPinService.getUserInfo(token));
//        System.out.println(youPinService.getUserInventoryDataList(token));

//        System.out.println(youPinService.getUserInventoryOnSellDataList(token));

//        System.out.println(Arrays.toString(youPinService.getGoodsInfo(token)));


//        System.out.println(youPinService.userSellInventory(token));

//        System.out.println(youPinService.userItemsOnSellPriceChange(token));

//        System.out.println(youPinService.userItemsOffSale(token));

    }
}
