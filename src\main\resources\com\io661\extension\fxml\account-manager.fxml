<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<AnchorPane xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.AccountManagerController"
            stylesheets="@../css/account-management.css">
    <children>
        <VBox spacing="15" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0"
              AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
            <padding>
                <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>

            <!-- 顶部标题和操作区 -->
            <HBox alignment="CENTER_LEFT" spacing="15">
                <Label text="Steam账号管理" styleClass="title-label">
                    <font>
                        <Font name="System Bold" size="18.0" />
                    </font>
                </Label>

                <Region HBox.hgrow="ALWAYS" />

                <HBox alignment="CENTER" spacing="10" styleClass="search-box">
                    <TextField fx:id="searchField" promptText="输入Steam ID或账号名" prefWidth="250.0"
                               styleClass="search-field" />
                    <Button fx:id="searchButton" mnemonicParsing="false" onAction="#handleSearch"
                            styleClass="icon-button">
                        <graphic>
                            <ImageView fitHeight="16.0" fitWidth="16.0">
                                <Image url="@../img/search.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                </HBox>

                <Button fx:id="addAccountButton" mnemonicParsing="false"
                        onAction="#handleAddAccount"
                        text="添加账号" styleClass="primary-button" />

                <MenuButton fx:id="batchOperationButton" mnemonicParsing="false" text="批量操作"
                            styleClass="menu-button">
                    <items>
                        <MenuItem mnemonicParsing="false" text="批量登录" onAction="#handleBatchLogin" />
                        <MenuItem mnemonicParsing="false" text="批量更新令牌"
                                  onAction="#handleBatchUpdateToken" />
                        <MenuItem mnemonicParsing="false" text="批量检查状态"
                                  onAction="#handleBatchCheckStatus" />
                    </items>
                </MenuButton>
            </HBox>

            <!-- 过滤和排序区 -->
            <HBox spacing="10" alignment="CENTER_LEFT" styleClass="filter-bar">
                <Label text="筛选:" />
                <ComboBox fx:id="statusFilter" promptText="账号状态" prefWidth="120.0" />
                <ComboBox fx:id="proxyFilter" promptText="代理设置" prefWidth="120.0" />

                <Region HBox.hgrow="ALWAYS" />

                <Label text="排序:" />
                <ComboBox fx:id="sortOptions" promptText="排序方式" prefWidth="120.0" />

                <ToggleButton fx:id="gridViewToggle" selected="true" styleClass="view-toggle">
                    <graphic>
                        <ImageView fitHeight="16.0" fitWidth="16.0">
                            <Image url="@../img/grid.png" />
                        </ImageView>
                    </graphic>
                </ToggleButton>

                <ToggleButton fx:id="listViewToggle" styleClass="view-toggle">
                    <graphic>
                        <ImageView fitHeight="16.0" fitWidth="16.0">
                            <Image url="@../img/list.png" />
                        </ImageView>
                    </graphic>
                </ToggleButton>
            </HBox>

            <!-- 账号卡片区域 -->
            <ScrollPane fitToWidth="true" styleClass="transparent-scroll-pane" VBox.vgrow="ALWAYS">
                <content>
                    <FlowPane fx:id="accountCardsContainer" hgap="15" vgap="15"
                              styleClass="cards-container">
                        <!-- 账号卡片将在控制器中动态添加 -->
                    </FlowPane>
                </content>
            </ScrollPane>

            <!-- 底部状态栏 -->
            <HBox alignment="CENTER_LEFT" spacing="15" styleClass="status-bar">
                <Label fx:id="accountCountLabel" text="共 0 个账号" />
                <Label fx:id="onlineCountLabel" text="在线: 0" styleClass="online-count" />
                <Label fx:id="offlineCountLabel" text="离线: 0" styleClass="offline-count" />

                <Region HBox.hgrow="ALWAYS" />

                <Button fx:id="checkStatusButton" mnemonicParsing="false"
                        text="检查登录状态" onAction="#handleCheckStatus" styleClass="secondary-button" />
                <Button fx:id="configProxyButton" mnemonicParsing="false"
                        text="代理配置" onAction="#handleConfigProxy" styleClass="secondary-button" />
            </HBox>
        </VBox>

        <!-- 添加账号弹出面板 -->
        <StackPane fx:id="addAccountPopup" visible="false" styleClass="popup-overlay"
                   AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0"
                   AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
            <VBox styleClass="popup-content" maxWidth="400.0" maxHeight="400.0" spacing="15">
                <HBox alignment="CENTER">
                    <Label text="添加新账号" styleClass="popup-title" />
                    <Region HBox.hgrow="ALWAYS" />
                    <Button fx:id="closePopupButton" mnemonicParsing="false"
                            onAction="#handleClosePopup"
                            styleClass="close-button">
                        <graphic>
                            <ImageView fitHeight="12.0" fitWidth="12.0">
                                <Image url="@../img/close.png" />
                            </ImageView>
                        </graphic>
                    </Button>
                </HBox>

                <Separator />

                <Button fx:id="loginBindSteam" mnemonicParsing="false" text="Steam账号登录"
                        onAction="#loginBindSteam" styleClass="popup-button" maxWidth="Infinity" />

                <Button fx:id="bindTokenButton" mnemonicParsing="false" text="绑定Steam令牌"
                        onAction="#handleBindToken" styleClass="popup-button" maxWidth="Infinity" />
                <Separator />
                <VBox styleClass="info-box" spacing="10" style="-fx-border-color: #DCDCDC; -fx-border-width: 1; -fx-padding: 10; -fx-border-radius: 4;">
                    <Label text="steam账号登陆：" style="-fx-font-weight: bold; -fx-text-fill: #e74c3c;" />
                    <Label text="通过该方法绑定可以管理自己的steam账号库存进行交易，但无法查看令牌，无法确认令牌" wrapText="true" />
                    <Label text="绑定steam令牌：" style="-fx-font-weight: bold; -fx-text-fill: #e74c3c;" />
                    <Label text="通过文件绑定后可以在本地查看令牌，管理自己的steam账号库存等，不限于收发报价，确认令牌报价等。" wrapText="true" />
                </VBox>
            </VBox>
        </StackPane>
    </children>
</AnchorPane>