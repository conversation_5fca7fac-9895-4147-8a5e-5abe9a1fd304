package com.io661.extension.controller;

import com.google.gson.Gson;
import com.io661.extension.IO661Extension;
import com.io661.extension.commonResult.CommonResult;
import com.io661.extension.model.Steam.SteamRes;
import com.io661.extension.model.SteamAccount;
import com.io661.extension.service.AccountManagerService;
import com.io661.extension.util.WebEngineManager;
import com.io661.extension.util.YouPin.YouPinCookieManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import lombok.Data;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.HashMap;
import java.util.HashSet;

@Data
public class AccountManagerController implements Initializable {
    @FXML
    public Button importSteamButton;

    @FXML
    public Button steamLoginButton;

    @FXML
    public Button loginBindSteam;

    @FXML
    public Button bindYouPinButton;

    @FXML
    public Button bindBuffButton;

    private AccountManagerService accountManagerService;

    // 平台绑定状态缓存
    private Map<String, Set<String>> platformBindingCache = new HashMap<>();
    private long cacheTimestamp = 0;
    private static final long CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

    // Steam控制器
    private SteamController steamController;
    @FXML
    private TextField searchField;

    @FXML
    private Button searchButton;

    @FXML
    private Button addAccountButton;

    @FXML
    private MenuButton batchOperationButton;

    @FXML
    private ComboBox<String> statusFilter;

    @FXML
    private ComboBox<String> proxyFilter;

    @FXML
    private ComboBox<String> sortOptions;

    @FXML
    private ToggleButton gridViewToggle;

    @FXML
    private ToggleButton listViewToggle;

    @FXML
    private FlowPane accountCardsContainer;

    @FXML
    private Label accountCountLabel;

    @FXML
    private Label onlineCountLabel;

    @FXML
    private Label offlineCountLabel;

    @FXML
    private Button checkStatusButton;

    @FXML
    private Button configProxyButton;

    @FXML
    private StackPane addAccountPopup;

    // 账号数据列表
    private ObservableList<SteamAccount> accounts = FXCollections.observableArrayList();
    // Steam账号数据列表
    private ObservableList<SteamRes.SteamBind> steamAccounts = FXCollections.observableArrayList();

    /**
     * 无参构造函数，用于FXML加载
     */
    public AccountManagerController() {
        this.accountManagerService = new com.io661.extension.service.Impl.AccountManagerServiceImpl();
        this.steamController = new SteamController();
    }

    /**
     * 带参构造函数，用于依赖注入
     */
    public AccountManagerController(AccountManagerService accountManagerService) {
        this.accountManagerService = accountManagerService;
        this.steamController = new SteamController();
    }

    /**
     * 获取绑定的Steam账号信息
     * @param token 授权令牌
     * @return 包含Steam账号列表的结果
     */
    public CommonResult<List<SteamRes.SteamBind>> getBindSteamInfo(String token) {
        List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(token);
        return CommonResult.webSuccess(0, "获取成功", steamBindList);
    }

    /**
     * 加载Steam账号数据
     */
    private void loadSteamAccounts() {
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法加载Steam账号");
            return;
        }

        // 在后台线程中加载数据
        new Thread(() -> {
            List<SteamRes.SteamBind> steamBindList = accountManagerService.getAllSteamAccount(token);

            // 在JavaFX线程中更新UI
            Platform.runLater(() -> {
                if (steamBindList != null && !steamBindList.isEmpty()) {
                    steamAccounts.clear();
                    steamAccounts.addAll(steamBindList);
                    System.out.println("成功加载 " + steamBindList.size() + " 个Steam账号");

                    // 渲染Steam账号卡片
                    renderSteamAccountCards();

                    // 更新账号统计信息
                    updateSteamAccountStats();
                } else {
                    System.out.println("未找到Steam账号或加载失败");
                }
            });
        }).start();
    }

    /**
     * 加载Steam账号数据
     */
    private void loadSteamData(String token) {
        try {
            String response = accountManagerService.getAllSteamAccount(token).toString();
            // 解析JSON数据
            Gson gson = new Gson();
            SteamRes steamRes = gson.fromJson(response, SteamRes.class);

            if (steamRes != null && steamRes.getSteamBindList() != null) {
                steamAccounts.clear();
                steamAccounts.addAll(steamRes.getSteamBindList());

                // 渲染Steam账号卡片
                renderSteamAccountCards();

                // 更新账号统计信息
                updateSteamAccountStats();
            }
        } catch (Exception e) {
            System.out.println("加载数据异常: " + e.getMessage());
        }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            if (accountManagerService == null) {
                accountManagerService = new com.io661.extension.service.Impl.AccountManagerServiceImpl();
            }

            // 初始化过滤和排序选项
            initializeFilters();

            // 初始化视图切换按钮组
            initializeViewToggle();

            // 加载Steam账号数据
            loadSteamAccounts();

            System.out.println("AccountManagerController初始化完成");
        } catch (Exception e) {
            System.err.println("初始化AccountManagerController时发生异常: " + e.getMessage());
        }
    }

    private void initializeFilters() {
        try {
            // 初始化状态过滤器
            if (statusFilter != null) {
                statusFilter.setItems(FXCollections.observableArrayList(
                        "全部状态", "在线", "离线", "错误"
                ));
                statusFilter.getSelectionModel().selectFirst();
            } else {
                System.err.println("statusFilter为空");
            }

            // 初始化代理过滤器
            if (proxyFilter != null) {
                proxyFilter.setItems(FXCollections.observableArrayList(
                        "全部代理", "内置代理", "无代理"
                ));
                proxyFilter.getSelectionModel().selectFirst();
            } else {
                System.err.println("proxyFilter为空");
            }

            // 初始化排序选项
            if (sortOptions != null) {
                sortOptions.setItems(FXCollections.observableArrayList(
                        "默认排序", "用户名 A-Z", "用户名 Z-A", "余额高到低", "余额低到高"
                ));
                sortOptions.getSelectionModel().selectFirst();
            } else {
                System.err.println("sortOptions为空");
            }

            // 添加监听器，当选择变化时重新渲染账号卡片
            if (statusFilter != null) {
                statusFilter.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> filterAndRenderAccounts());
            }

            if (proxyFilter != null) {
                proxyFilter.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> filterAndRenderAccounts());
            }

            if (sortOptions != null) {
                sortOptions.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> filterAndRenderAccounts());
            }

            System.out.println("过滤器初始化完成");
        } catch (Exception e) {
            System.err.println("初始化过滤器时发生异常: " + e.getMessage());
        }
    }

    private void initializeViewToggle() {
        try {
            System.out.println("初始化视图切换按钮...");

            // 确保只有一个视图按钮被选中
            if (gridViewToggle != null) {
                gridViewToggle.setOnAction(event -> {
                    gridViewToggle.setSelected(true);
                    if (listViewToggle != null) {
                        listViewToggle.setSelected(false);
                    }
                    if (accountCardsContainer != null) {
                        accountCardsContainer.getStyleClass().remove("list-view-mode");
                        accountCardsContainer.getStyleClass().add("grid-view-mode");
                    }
                });
            } else {
                System.err.println("gridViewToggle为空");
            }

            if (listViewToggle != null) {
                listViewToggle.setOnAction(event -> {
                    listViewToggle.setSelected(true);
                    if (gridViewToggle != null) {
                        gridViewToggle.setSelected(false);
                    }
                    if (accountCardsContainer != null) {
                        accountCardsContainer.getStyleClass().remove("grid-view-mode");
                        accountCardsContainer.getStyleClass().add("list-view-mode");
                    }
                });
            } else {
                System.err.println("listViewToggle为空");
            }

            // 默认选中网格视图
            if (gridViewToggle != null) {
                gridViewToggle.setSelected(true);
            }

            if (listViewToggle != null) {
                listViewToggle.setSelected(false);
            }

            if (accountCardsContainer != null) {
                accountCardsContainer.getStyleClass().add("grid-view-mode");
            } else {
                System.err.println("accountCardsContainer为空");
            }

            System.out.println("视图切换按钮初始化完成");
        } catch (Exception e) {
            System.err.println("初始化视图切换按钮时发生异常: " + e.getMessage());
        }
    }

    private void updateAccountStats() {
        // 更新账号统计信息
        int totalAccounts = accounts.size();
        int onlineAccounts = (int) accounts.stream()
                .filter(a -> a.getStatus() == SteamAccount.AccountStatus.NORMAL)
                .count();
        int offlineAccounts = totalAccounts - onlineAccounts;

        accountCountLabel.setText("共 " + totalAccounts + " 个账号");
        onlineCountLabel.setText("在线: " + onlineAccounts);
        offlineCountLabel.setText("离线: " + offlineAccounts);
    }

    private void filterAndRenderAccounts() {
        // 根据过滤条件筛选账号
        // 这里简单实现，实际应用中可能需要更复杂的逻辑
        renderAccountCards();
    }

    private void renderAccountCards() {
        // 清空现有卡片
        accountCardsContainer.getChildren().clear();

        // 为每个账号创建卡片
        for (SteamAccount account : accounts) {
            try {
                // 加载卡片FXML
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/account-card.fxml"));
                Node cardNode = loader.load();

                // 配置卡片数据
                configureAccountCard(cardNode, account);

                // 添加到容器
                accountCardsContainer.getChildren().add(cardNode);
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
    }

    private void configureAccountCard(Node cardNode, SteamAccount account) {
        // 设置卡片数据 - 简化版本
        Label usernameLabel = (Label) ((VBox) cardNode).lookup("#usernameLabel");

        if (usernameLabel != null) {
            usernameLabel.setText(account.getAccountName());
        }
    }

    @FXML
    private void handleSearch() {
        String searchTerm = searchField.getText().trim().toLowerCase();
        if (searchTerm.isEmpty()) {
            renderAccountCards();
            return;
        }

        ObservableList<SteamAccount> filteredAccounts = accounts.filtered(
                account -> account.getAccountName().toLowerCase().contains(searchTerm)
        );

        // 清空现有卡片
        accountCardsContainer.getChildren().clear();

        // 为过滤后的账号创建卡片
        for (SteamAccount account : filteredAccounts) {
            try {
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/account-card.fxml"));
                Node cardNode = loader.load();
                configureAccountCard(cardNode, account);
                accountCardsContainer.getChildren().add(cardNode);
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
    }

    @FXML
    private void handleAddAccount() {
        // 显示添加账号弹出面板
        addAccountPopup.setVisible(true);
    }

    @FXML
    private void handleClosePopup() {
        // 隐藏添加账号弹出面板
        addAccountPopup.setVisible(false);
    }

    @FXML
    private void handleImportSteam() {
        // 获取授权令牌
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法导入Steam账号");
            return;
        }

        // 加载Steam账号数据
        loadSteamAccounts();

        System.out.println("导入Steam账号");
        handleClosePopup();
    }

    @FXML
    private void handleImportMaFile() {
        // 处理批量Steam账号导入
        System.out.println("批量导入Steam账号");
        handleClosePopup();
    }



    @FXML
    private void handleBatchSteamLogin() {
        // 处理批量Steam账号登录
        System.out.println("批量Steam账号登录");


        // 显示批量绑定Steam账号界面
        showBatchSteamLoginDialog();
    }

    /**
     * 显示批量绑定Steam账号对话框
     */
    private void showBatchSteamLoginDialog() {
        // 使用SteamController显示批量绑定对话框
        steamController.showBatchBindDialog();
    }

    @FXML
    private void handleBatchLogin() {
        // 处理批量登录
        System.out.println("批量登录账号");
    }

    @FXML
    private void handleBatchUpdateToken() {
        // 处理批量更新令牌
        System.out.println("批量更新令牌");
    }

    @FXML
    private void handleBatchCheckStatus() {
        // 处理批量检查状态
        System.out.println("批量检查账号状态");
    }

    @FXML
    private void handleCheckStatus() {
        // 处理检查登录状态
        System.out.println("检查账号登录状态");
    }

    @FXML
    private void handleConfigProxy() {
        // 处理代理配置
        System.out.println("配置代理");
    }

    /**
     * 渲染Steam账号卡片
     */
    private void renderSteamAccountCards() {
        // 清空现有卡片
        accountCardsContainer.getChildren().clear();

        // 为每个Steam账号创建卡片
        for (SteamRes.SteamBind steamAccount : steamAccounts) {
            try {
                // 加载卡片FXML
                FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/account-card.fxml"));
                Node cardNode = loader.load();

                // 配置卡片数据
                configureSteamAccountCard(cardNode, steamAccount);

                // 添加到容器
                accountCardsContainer.getChildren().add(cardNode);
            } catch (IOException e) {
                System.out.println("渲染Steam账号卡片异常: " + e.getMessage());
            }
        }
    }

    /**
     * 配置Steam账号卡片
     * @param cardNode 卡片节点
     * @param steamAccount Steam账号数据
     */
    private void configureSteamAccountCard(Node cardNode, SteamRes.SteamBind steamAccount) {
        try {
            // 获取卡片中的UI元素
            Label usernameLabel = (Label) ((VBox) cardNode).lookup("#usernameLabel");
            ImageView avatarImage = (ImageView) ((VBox) cardNode).lookup("#avatarImage");
            ImageView accountTypeIcon = (ImageView) ((VBox) cardNode).lookup("#accountTypeIcon");
            Label balanceLabel = (Label) ((VBox) cardNode).lookup("#balanceLabel");
            Label ipConfigLabel = (Label) ((VBox) cardNode).lookup("#ipConfigLabel");
            Label accountIdLabel = (Label) ((VBox) cardNode).lookup("#accountIdLabel");
            Label tradeUrlLabel = (Label) ((VBox) cardNode).lookup("#tradeUrlLabel");

            // 设置昵称
            if (usernameLabel != null) {
                usernameLabel.setText(steamAccount.getNickname());
            }

            // 设置账号类型图标（机器人/人工）
            if (accountTypeIcon != null) {
                if (steamAccount.isQuick()) {
                    // 机器人账号
                    accountTypeIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/robot.png"))));
                } else {
                    // 人工账号
                    accountTypeIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/human.png"))));
                }
            }

            // 设置账号状态（有效/无效）
            if (balanceLabel != null) {
                balanceLabel.getStyleClass().removeAll("effective", "ineffective");
                if (steamAccount.isEffective()) {
                    balanceLabel.setText("账号状态: 有效");
                    balanceLabel.getStyleClass().add("effective");
                } else {
                    balanceLabel.setText("账号状态: 无效");
                    balanceLabel.getStyleClass().add("ineffective");
                }
            }

            // 设置Steam ID
            if (accountIdLabel != null) {
                accountIdLabel.setText(steamAccount.getSteamId());
            }

            // 设置交易URL
            if (tradeUrlLabel != null) {
                tradeUrlLabel.setText(steamAccount.getTradeUrl());
            }

            // 设置IP配置
            if (ipConfigLabel != null) {
                ipConfigLabel.setText("内置代理");
            }

            // 设置头像
            if (avatarImage != null) {
                String avatarUrl = steamAccount.getAvatar();
                if (avatarUrl != null && !avatarUrl.isEmpty()) {
                    try {
                        Image avatar = new Image(avatarUrl, true);
                        avatarImage.setImage(avatar);
                    } catch (Exception e) {
                        System.out.println("加载头像失败: " + e.getMessage());
                        // 使用默认头像
                        avatarImage.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/avatar.png"))));
                    }
                }
            }

            // 设置查看/绑定令牌按钮
            Button viewTokenButton = (Button) ((VBox) cardNode).lookup("#viewTokenButton");
            if (viewTokenButton != null) {
                // 根据quick标志设置按钮文本
                if (steamAccount.isQuick()) {
                    viewTokenButton.setText("查看令牌");
                } else {
                    viewTokenButton.setText("绑定令牌");
                }

                // 设置按钮点击事件
                if (steamAccount.isQuick()) {
                    // quick=true，查看令牌
                    viewTokenButton.setOnAction(event -> handleViewToken(steamAccount));
                } else {
                    // quick=false，绑定本地令牌
                    viewTokenButton.setOnAction(event -> handleBindLocalToken(steamAccount));
                }
            }

            // 显示已绑定平台
            HBox boundPlatformsBox = (HBox) ((VBox) cardNode).lookup("#boundPlatformsBox");
            if (boundPlatformsBox != null) {
                boundPlatformsBox.getChildren().clear();

                // 检查账号绑定的平台
                Set<String> boundPlatforms = checkPlatformBindings(steamAccount.getSteamId());

                // 为每个绑定的平台添加图标
                for (String platform : boundPlatforms) {
                    try {
                        ImageView platformIcon = new ImageView();
                        platformIcon.setFitHeight(16);
                        platformIcon.setFitWidth(16);

                        // 根据平台类型设置图标
                        if ("youpin".equals(platform)) {
                            platformIcon.setImage(new Image(Objects.requireNonNull(getClass().getResourceAsStream("/com/io661/extension/img/youpin.png"))));
                            Tooltip tooltip = new Tooltip("悠悠有品");
                            Tooltip.install(platformIcon, tooltip);
                        }

                        boundPlatformsBox.getChildren().add(platformIcon);
                    } catch (Exception e) {
                        System.out.println("添加平台图标异常: " + e.getMessage());
                    }
                }
            }

            // 设置设置按钮点击事件
            Button settingsButton = (Button) ((VBox) cardNode).lookup("#settingsButton");
            if (settingsButton != null) {
                settingsButton.setOnAction(event -> showSettingsMenu(settingsButton, steamAccount));
            }

        } catch (Exception e) {
            System.out.println("配置Steam账号卡片异常: " + e.getMessage());
        }
    }

    /**
     * 更新Steam账号统计信息
     */
    private void updateSteamAccountStats() {
        int totalAccounts = steamAccounts.size();
        int effectiveAccounts = (int) steamAccounts.stream()
                .filter(SteamRes.SteamBind::isEffective)
                .count();
        int quickAccounts = (int) steamAccounts.stream()
                .filter(SteamRes.SteamBind::isQuick)
                .count();

        accountCountLabel.setText("共 " + totalAccounts + " 个账号");
        onlineCountLabel.setText("有效: " + effectiveAccounts);
        offlineCountLabel.setText("急速: " + quickAccounts);
    }

    /**
     * 处理查看令牌按钮点击
     * @param steamAccount Steam账号数据
     */
    private void handleViewToken(SteamRes.SteamBind steamAccount) {
        System.out.println("查看令牌: " + steamAccount.getNickname() + " (" + steamAccount.getSteamId() + ")");

        // 使用SteamController显示令牌对话框
        steamController.showTokenDialog(
                steamAccount.getSteamId(),
                steamAccount.getNickname(),
                steamAccount.isQuick()
        );
    }

    /**
     * 处理绑定本地令牌（针对quick=false的账号）
     * @param steamAccount Steam账号数据
     */
    private void handleBindLocalToken(SteamRes.SteamBind steamAccount) {
        System.out.println("绑定本地令牌: " + steamAccount.getNickname() + " (" + steamAccount.getSteamId() + ")");

        // 获取授权令牌
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法绑定Steam令牌");

            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("未找到授权令牌，无法绑定Steam令牌");
                alert.showAndWait();
            });

            return;
        }

        // 创建文件选择器
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("选择.maFile文件");
        fileChooser.getExtensionFilters().add(
                new javafx.stage.FileChooser.ExtensionFilter("Steam认证文件", "*.maFile")
        );

        // 显示文件选择器
        java.io.File selectedFile = fileChooser.showOpenDialog(IO661Extension.getMainStage());
        if (selectedFile != null) {
            try {
                // 读取文件内容
                String maFileContent = new String(java.nio.file.Files.readAllBytes(selectedFile.toPath()));

                // 使用SteamController显示密码对话框并处理绑定
                final String finalToken = token;
                steamController.showPasswordDialog(maFileContent, password -> {
                    // 绑定令牌
                    steamController.bindSteamToken(maFileContent, password, finalToken, success -> {
                        if (success) {
                            // 显示成功提示
                            Platform.runLater(() -> {
                                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                                alert.setTitle("成功");
                                alert.setHeaderText(null);
                                alert.setContentText("Steam令牌绑定成功");
                                alert.showAndWait();

                                // 重新加载Steam账号数据
                                loadSteamAccounts();
                            });
                        } else {
                            // 显示错误提示
                            Platform.runLater(() -> {
                                Alert alert = new Alert(Alert.AlertType.ERROR);
                                alert.setTitle("错误");
                                alert.setHeaderText(null);
                                alert.setContentText("Steam令牌绑定失败");
                                alert.showAndWait();
                            });
                        }
                    });
                });
            } catch (Exception e) {
                System.err.println("读取.maFile文件失败: " + e.getMessage());
                e.printStackTrace();

                // 显示错误提示
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("读取.maFile文件失败: " + e.getMessage());
                alert.showAndWait();
            }
        }
    }

    /**
     * 处理绑定令牌按钮点击（从添加账号弹窗）
     */
    @FXML
    private void handleBindToken() {
        System.out.println("绑定Steam令牌");

        // 获取授权令牌
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法绑定Steam令牌");

            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("未找到授权令牌，无法绑定Steam令牌");
                alert.showAndWait();
            });

            return;
        }

        // 创建文件选择器
        javafx.stage.FileChooser fileChooser = new javafx.stage.FileChooser();
        fileChooser.setTitle("选择.maFile文件");
        fileChooser.getExtensionFilters().add(
                new javafx.stage.FileChooser.ExtensionFilter("Steam认证文件", "*.maFile")
        );

        // 显示文件选择器
        java.io.File selectedFile = fileChooser.showOpenDialog(IO661Extension.getMainStage());
        if (selectedFile != null) {
            try {
                // 读取文件内容
                String maFileContent = new String(java.nio.file.Files.readAllBytes(selectedFile.toPath()));

                // 使用SteamController显示密码对话框并处理绑定
                final String finalToken = token;
                steamController.showPasswordDialog(maFileContent, password -> {
                    // 绑定令牌
                    steamController.bindSteamToken(maFileContent, password, finalToken, success -> {
                        if (success) {
                            // 显示成功提示
                            Platform.runLater(() -> {
                                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                                alert.setTitle("成功");
                                alert.setHeaderText(null);
                                alert.setContentText("Steam令牌绑定成功");
                                alert.showAndWait();

                                // 重新加载Steam账号数据
                                loadSteamAccounts();
                            });
                        } else {
                            // 显示错误提示
                            Platform.runLater(() -> {
                                Alert alert = new Alert(Alert.AlertType.ERROR);
                                alert.setTitle("错误");
                                alert.setHeaderText(null);
                                alert.setContentText("Steam令牌绑定失败");
                                alert.showAndWait();
                            });
                        }
                    });
                });
            } catch (Exception e) {
                System.err.println("读取.maFile文件失败: " + e.getMessage());
                e.printStackTrace();

                // 显示错误提示
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("读取.maFile文件失败: " + e.getMessage());
                alert.showAndWait();
            }
        }

        // 关闭弹出窗口
        handleClosePopup();
    }

    /**
     * 处理绑定悠悠有品账号按钮点击
     */
    @FXML
    private void handleBindYouPin() {
        System.out.println("绑定悠悠有品账号");

        // 关闭当前弹窗
        handleClosePopup();

        // 显示悠悠有品登录对话框
        showYouPinLoginDialog();
    }

    /**
     * 处理绑定BUFF账号按钮点击
     */
    @FXML
    private void handleBindBuff() {
        System.out.println("绑定BUFF账号");

        // 关闭当前弹窗
        handleClosePopup();

        // TODO: 实现BUFF账号绑定逻辑
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText("BUFF账号绑定功能正在开发中...");
            alert.showAndWait();
        });
    }

    /**
     * 显示设置菜单
     * @param button 设置按钮
     * @param steamAccount Steam账号数据
     */
    private void showSettingsMenu(Button button, SteamRes.SteamBind steamAccount) {
        // 创建上下文菜单
        ContextMenu contextMenu = new ContextMenu();

        // 创建菜单项
        MenuItem unbindTokenItem = new MenuItem("解绑");
        MenuItem otherOperationItem = new MenuItem("其他操作");

        // 设置解绑令牌菜单项点击事件
        unbindTokenItem.setOnAction(event -> handleUnbindToken(steamAccount));

        // 设置其他操作菜单项点击事件
        otherOperationItem.setOnAction(event -> {
            System.out.println("其他操作: " + steamAccount.getNickname());
            // 这里可以添加其他操作的处理逻辑
        });

        // 添加菜单项到上下文菜单
        contextMenu.getItems().addAll(unbindTokenItem, otherOperationItem);

        // 显示上下文菜单
        contextMenu.show(button, javafx.geometry.Side.BOTTOM, 0, 0);
    }

    /**
     * 处理解绑令牌
     * @param steamAccount Steam账号数据
     */
    private void handleUnbindToken(SteamRes.SteamBind steamAccount) {
        System.out.println("解绑令牌: " + steamAccount.getNickname() + " (" + steamAccount.getSteamId() + ")");

        // 获取授权令牌
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            System.out.println("未找到授权令牌，无法解绑Steam令牌");

            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("未找到授权令牌，无法解绑Steam令牌");
                alert.showAndWait();
            });

            return;
        }

        // 使用SteamController显示解绑确认对话框并处理解绑
        final String finalToken = token;
        steamController.showUnbindConfirmDialog(steamAccount, finalToken, success -> {
            if (success) {
                // 显示成功提示
                Platform.runLater(() -> {
                    Alert alert = new Alert(Alert.AlertType.INFORMATION);
                    alert.setTitle("成功");
                    alert.setHeaderText(null);
                    alert.setContentText("Steam令牌解绑成功");
                    alert.showAndWait();

                    // 重新加载Steam账号数据
                    loadSteamAccounts();
                });
            } else {
                // 用户取消或解绑失败，不显示错误提示
                System.out.println("用户取消解绑或解绑失败");
            }
        });
    }

    /**
     * Steam账号登录方法
     * 打开浏览器让用户登录Steam，获取cookies后发送到服务器
     */
    public void loginBindSteam() {
        // 获取授权令牌
        String token = SteamController.getAuthToken();
        if (token == null || token.isEmpty()) {
            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("未找到授权令牌，请先登录应用");
                alert.showAndWait();
            });
            return;
        }

        // 打开浏览器获取Steam cookies
        String result = WebEngineManager.startGetSteamCookies();

        // 发送cookies到服务器并处理结果
        boolean success = accountManagerService.loginSteamAccount(result);

        // 显示结果提示
        Platform.runLater(() -> {
            Alert alert = new Alert(success ? Alert.AlertType.INFORMATION : Alert.AlertType.ERROR);
            alert.setTitle(success ? "成功" : "失败");
            alert.setHeaderText(null);
            alert.setContentText(success ? "Steam账号登录成功" : "Steam账号登录失败，请重试");
            alert.showAndWait();

            // 如果登录成功，刷新Steam账号列表
            if (success) {
                loadSteamAccounts();
            }
        });
    }

    /**
     * 显示悠悠有品登录对话框
     */
    private void showYouPinLoginDialog() {
        try {
            // 加载悠悠有品登录对话框FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/youpin-login-dialog.fxml"));
            VBox dialogRoot = loader.load();

            // 获取控制器
            YouPinLoginDialogController controller = loader.getController();

            // 设置登录完成回调
            controller.setOnLoginComplete(success -> {
                if (success) {
                    // 登录成功，刷新账号列表以更新平台绑定状态
                    loadSteamAccounts();
                }
            });

            // 创建对话框
            Stage dialogStage = new Stage();
            dialogStage.setTitle("绑定悠悠有品账号");
            dialogStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            dialogStage.initOwner(IO661Extension.getMainStage());
            dialogStage.setResizable(false);

            // 设置场景
            javafx.scene.Scene scene = new javafx.scene.Scene(dialogRoot);
            dialogStage.setScene(scene);

            // 显示对话框
            dialogStage.showAndWait();

        } catch (Exception e) {
            System.err.println("显示悠悠有品登录对话框失败: " + e.getMessage());
            e.printStackTrace();

            // 显示错误提示
            Platform.runLater(() -> {
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("错误");
                alert.setHeaderText(null);
                alert.setContentText("打开悠悠有品登录对话框失败: " + e.getMessage());
                alert.showAndWait();
            });
        }
    }

    /**
     * 检查账号绑定的平台
     * @param steamId Steam账号ID
     * @return 绑定的平台集合
     */
    private Set<String> checkPlatformBindings(String steamId) {
        // 如果缓存中存在，直接返回
        if (platformBindingCache.containsKey(steamId)) {
            return platformBindingCache.get(steamId);
        }

        // 创建新的平台集合
        Set<String> boundPlatforms = new HashSet<>();

        // 检查悠悠有品绑定状态
        if (YouPinCookieManager.youPinCookieExists(steamId)) {
            boundPlatforms.add("youpin");
        }

        // 将结果存入缓存
        platformBindingCache.put(steamId, boundPlatforms);
        return boundPlatforms;
    }
}